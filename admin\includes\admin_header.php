<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Admin Panel - Flori Construction</title>

    <!-- Performance Optimized CSS Loading -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Font Awesome 6.5 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" crossorigin="anonymous">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" crossorigin="anonymous">

    <!-- Google Fonts - Modern Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" crossorigin="anonymous">
    <!-- Modern Admin CSS -->
    <style>
        :root {
            /* Modern Color Palette */
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --primary-light: #e0e7ff;
            --secondary-color: #6b7280;
            --accent-color: #06b6d4;
            --accent-hover: #0891b2;

            /* Sidebar Colors */
            --sidebar-bg: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            --sidebar-hover: rgba(79, 70, 229, 0.1);
            --sidebar-active: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            --sidebar-width: 280px;

            /* Status Colors */
            --success-color: #059669;
            --success-light: #d1fae5;
            --warning-color: #d97706;
            --warning-light: #fef3c7;
            --danger-color: #dc2626;
            --danger-light: #fee2e2;
            --info-color: #0284c7;
            --info-light: #dbeafe;

            /* Background Colors */
            --body-bg: #f8fafc;
            --card-bg: #ffffff;
            --surface-bg: #f1f5f9;
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;

            /* Text Colors */
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #94a3b8;
            --text-light: #cbd5e1;

            /* Shadows */
            --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;

            /* Typography */
            --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-family-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

            /* Transitions */
            --transition-fast: 0.15s ease-in-out;
            --transition-normal: 0.3s ease-in-out;
            --transition-slow: 0.5s ease-in-out;
        }

        /* Base Styles */
        * {
            box-sizing: border-box;
        }

        *::before,
        *::after {
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family-primary);
            font-size: 0.875rem;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--body-bg);
            font-weight: 400;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--font-family-heading);
            font-weight: 600;
            line-height: 1.3;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
        }

        h1 { font-size: 2rem; font-weight: 700; }
        h2 { font-size: 1.75rem; font-weight: 600; }
        h3 { font-size: 1.5rem; font-weight: 600; }
        h4 { font-size: 1.25rem; font-weight: 600; }
        h5 { font-size: 1.125rem; font-weight: 600; }
        h6 { font-size: 1rem; font-weight: 600; }

        p {
            margin-bottom: 1rem;
            color: var(--text-secondary);
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition-fast);
        }

        a:hover {
            color: var(--primary-hover);
            text-decoration: none;
        }

        /* Modern Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 1000;
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            transition: var(--transition-normal);
        }

        .sidebar-sticky {
            position: relative;
            height: 100vh;
            padding: 2rem 0 1rem;
            overflow-x: hidden;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
        }

        .sidebar-sticky::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-sticky::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar-sticky::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        /* Sidebar Brand */
        .sidebar-brand {
            display: flex;
            align-items: center;
            padding: 1.5rem 1.5rem 2rem;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .brand-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            margin-right: 1rem;
            box-shadow: var(--shadow-md);
        }

        .brand-text h5 {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 1.125rem;
        }

        .brand-text small {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.75rem;
        }

        /* Navigation Links */
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.875rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: var(--radius-lg);
            font-weight: 500;
            font-size: 0.875rem;
            transition: var(--transition-normal);
            position: relative;
            display: flex;
            align-items: center;
            border: 1px solid transparent;
        }

        .sidebar .nav-link:hover {
            color: white;
            background: var(--sidebar-hover);
            transform: translateX(4px);
            border-color: rgba(79, 70, 229, 0.3);
        }

        .sidebar .nav-link.active {
            color: white;
            background: var(--sidebar-active);
            box-shadow: var(--shadow-md);
            border-color: rgba(79, 70, 229, 0.5);
        }

        .sidebar .nav-link.active::before {
            content: '';
            position: absolute;
            left: -1rem;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: var(--primary-color);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 8px rgba(79, 70, 229, 0.5);
        }

        .sidebar .nav-link i {
            margin-right: 0.875rem;
            width: 20px;
            font-size: 1rem;
            text-align: center;
        }

        /* Sidebar Sections */
        .sidebar-heading {
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            color: rgba(255, 255, 255, 0.5);
            margin: 2rem 1.5rem 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Badge in sidebar */
        .sidebar .badge {
            font-size: 0.6rem;
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            margin-left: auto;
        }

        /* Modern Top Navbar */
        .top-navbar {
            position: fixed;
            top: 0;
            left: var(--sidebar-width);
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-light);
            box-shadow: var(--shadow-sm);
            z-index: 999;
            display: flex;
            align-items: center;
            padding: 0 2rem;
            transition: var(--transition-normal);
        }

        .navbar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .breadcrumb-nav {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Search Bar */
        .search-container {
            position: relative;
            width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--card-bg);
            font-size: 0.875rem;
            transition: var(--transition-fast);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.875rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Quick Actions */
        .quick-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quick-action-btn {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition-fast);
            position: relative;
        }

        .quick-action-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .notification-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 0.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            border: 2px solid white;
        }

        /* Main Content Area */
        .main-content {
            margin-left: var(--sidebar-width);
            padding-top: 70px;
            min-height: 100vh;
            background: var(--body-bg);
            transition: var(--transition-normal);
        }

        .content-wrapper {
            padding: 2rem;
        }

        /* Page Header */
        .page-header {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
        }

        .page-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: var(--text-secondary);
            margin-bottom: 0;
            font-size: 1rem;
        }

        /* Modern Cards */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .card-header {
            background: var(--surface-bg);
            border-bottom: 1px solid var(--border-light);
            padding: 1.5rem 2rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-body {
            padding: 2rem;
        }

        .card-footer {
            background: var(--surface-bg);
            border-top: 1px solid var(--border-light);
            padding: 1rem 2rem;
        }

        /* Statistics Cards */
        .stats-card {
            background: var(--card-bg);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            padding: 2rem;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            height: 100%;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stats-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Color Variants */
        .border-left-primary { border-left: 4px solid var(--primary-color) !important; }
        .border-left-success { border-left: 4px solid var(--success-color) !important; }
        .border-left-info { border-left: 4px solid var(--info-color) !important; }
        .border-left-warning { border-left: 4px solid var(--warning-color) !important; }
        .border-left-danger { border-left: 4px solid var(--danger-color) !important; }

        .text-primary { color: var(--primary-color) !important; }
        .text-success { color: var(--success-color) !important; }
        .text-info { color: var(--info-color) !important; }
        .text-warning { color: var(--warning-color) !important; }
        .text-danger { color: var(--danger-color) !important; }

        .bg-primary-light { background-color: var(--primary-light) !important; }
        .bg-success-light { background-color: var(--success-light) !important; }
        .bg-info-light { background-color: var(--info-light) !important; }
        .bg-warning-light { background-color: var(--warning-light) !important; }
        .bg-danger-light { background-color: var(--danger-light) !important; }

        /* Modern Buttons */
        .btn {
            font-weight: 500;
            border-radius: var(--radius-lg);
            transition: var(--transition-normal);
            font-size: 0.875rem;
            padding: 0.75rem 1.5rem;
            border: 1px solid transparent;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-hover), #3730a3);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            color: white;
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .btn-secondary:hover {
            background: #374151;
            border-color: #374151;
            transform: translateY(-1px);
            color: white;
        }

        .btn-outline-primary {
            background: transparent;
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-secondary {
            background: transparent;
            color: var(--secondary-color);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background: #047857;
            border-color: #047857;
            transform: translateY(-1px);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background: #b45309;
            border-color: #b45309;
            transform: translateY(-1px);
            color: white;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
            border-color: var(--danger-color);
        }

        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
            transform: translateY(-1px);
            color: white;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.8125rem;
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1rem;
        }

        /* Button Groups */
        .btn-group .btn {
            border-radius: 0;
        }

        .btn-group .btn:first-child {
            border-radius: var(--radius-lg) 0 0 var(--radius-lg);
        }

        .btn-group .btn:last-child {
            border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
        }

        .btn-group .btn:only-child {
            border-radius: var(--radius-lg);
        }

        /* Modern Forms */
        .form-control {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            transition: var(--transition-fast);
            background: var(--card-bg);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-select {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 0.75rem 1rem;
            background: var(--card-bg);
            transition: var(--transition-fast);
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .input-group {
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .input-group-text {
            background: var(--surface-bg);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
        }

        /* Modern Tables */
        .table {
            border-collapse: separate;
            border-spacing: 0;
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .table thead th {
            background: var(--surface-bg);
            border-bottom: 2px solid var(--border-light);
            font-weight: 600;
            font-size: 0.8125rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-secondary);
            padding: 1.25rem 1.5rem;
            border-top: none;
        }

        .table tbody td {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--border-light);
            vertical-align: middle;
            color: var(--text-primary);
        }

        .table tbody tr:hover {
            background: var(--surface-bg);
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table-responsive {
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
        }

        /* Modern Badges */
        .badge {
            font-weight: 500;
            font-size: 0.75rem;
            padding: 0.5rem 0.875rem;
            border-radius: var(--radius-md);
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .badge.bg-success {
            background: var(--success-color) !important;
            color: white;
        }

        .badge.bg-warning {
            background: var(--warning-color) !important;
            color: white;
        }

        .badge.bg-info {
            background: var(--info-color) !important;
            color: white;
        }

        .badge.bg-danger {
            background: var(--danger-color) !important;
            color: white;
        }

        .badge.bg-primary {
            background: var(--primary-color) !important;
            color: white;
        }

        .badge.bg-secondary {
            background: var(--secondary-color) !important;
            color: white;
        }

        /* Modern Alerts */
        .alert {
            border: none;
            border-radius: var(--radius-lg);
            padding: 1.25rem 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid;
            box-shadow: var(--shadow-sm);
        }

        .alert-success {
            background: var(--success-light);
            color: #065f46;
            border-left-color: var(--success-color);
        }

        .alert-danger {
            background: var(--danger-light);
            color: #991b1b;
            border-left-color: var(--danger-color);
        }

        .alert-warning {
            background: var(--warning-light);
            color: #92400e;
            border-left-color: var(--warning-color);
        }

        .alert-info {
            background: var(--info-light);
            color: #1e40af;
            border-left-color: var(--info-color);
        }

        .alert-dismissible .btn-close {
            padding: 1rem 1.25rem;
        }

        /* Dropdown Styles */
        .dropdown-menu {
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            padding: 0.5rem 0;
            min-width: 200px;
            background: var(--card-bg);
        }

        .dropdown-item {
            padding: 0.75rem 1.25rem;
            font-size: 0.875rem;
            transition: var(--transition-fast);
            color: var(--text-primary);
        }

        .dropdown-item:hover {
            background: var(--surface-bg);
            color: var(--text-primary);
        }

        .dropdown-item:active {
            background: var(--primary-color);
            color: white;
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border-color: var(--border-light);
        }

        /* User Profile Dropdown */
        .user-dropdown {
            min-width: 280px;
        }

        .user-info {
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .user-email {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        /* Responsive Design */
        @media (max-width: 1199.98px) {
            .search-container {
                width: 250px;
            }
        }

        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform var(--transition-normal);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .top-navbar {
                left: 0;
            }

            .search-container {
                display: none;
            }

            .content-wrapper {
                padding: 1.5rem;
            }

            .page-header {
                padding: 1.5rem;
            }

            .stats-card {
                padding: 1.5rem;
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 767.98px) {
            .content-wrapper {
                padding: 1rem;
            }

            .page-header {
                padding: 1rem;
            }

            .page-header h1 {
                font-size: 1.5rem;
            }

            .stats-card {
                padding: 1rem;
            }

            .stats-number {
                font-size: 2rem;
            }

            .card-body {
                padding: 1rem;
            }

            .table thead th,
            .table tbody td {
                padding: 0.75rem;
                font-size: 0.8125rem;
            }

            .btn {
                padding: 0.625rem 1.25rem;
                font-size: 0.8125rem;
            }

            .quick-actions {
                gap: 0.25rem;
            }

            .quick-action-btn {
                width: 36px;
                height: 36px;
            }
        }

        @media (max-width: 575.98px) {
            .content-wrapper {
                padding: 0.75rem;
            }

            .page-header {
                padding: 0.75rem;
                margin-bottom: 1rem;
            }

            .stats-card {
                padding: 0.75rem;
            }

            .card-body {
                padding: 0.75rem;
            }
        }

        /* Utility Classes */
        .text-xs {
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .shadow-sm { box-shadow: var(--shadow-sm); }
        .shadow { box-shadow: var(--shadow-md); }
        .shadow-lg { box-shadow: var(--shadow-lg); }

        .rounded-lg { border-radius: var(--radius-lg); }
        .rounded-xl { border-radius: var(--radius-xl); }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in {
            animation: slideIn 0.3s ease-in-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }





        .navbar-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
            padding: 0 2rem 0 0;
            max-width: 100%;
        }

        /* Left Section */
        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: var(--radius-md);
            transition: all 0.2s ease-in-out;
            flex-direction: column;
            gap: 3px;
            width: 40px;
            height: 40px;
            justify-content: center;
            align-items: center;
        }

        .mobile-menu-toggle:hover {
            background-color: var(--light-bg);
        }

        .hamburger-line {
            width: 20px;
            height: 2px;
            background-color: var(--text-primary);
            border-radius: 2px;
            transition: all 0.3s ease-in-out;
        }

        .mobile-menu-toggle.collapsed .hamburger-line:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .mobile-menu-toggle.collapsed .hamburger-line:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.collapsed .hamburger-line:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.2s ease-in-out;
        }

        .navbar-brand:hover {
            color: var(--primary-color);
            text-decoration: none;
        }

        .brand-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            box-shadow: var(--shadow-md);
        }

        .brand-text {
            line-height: 1.2;
        }

        .brand-name {
            font-family: 'Plus Jakarta Sans', sans-serif;
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--text-primary);
            margin-bottom: 0;
        }

        .brand-subtitle {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Center Section - Search */
        .navbar-center {
            flex: 1;
            max-width: 500px;
            margin: 0 2rem;
        }

        .search-container {
            position: relative;
            width: 100%;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 0.75rem 1rem;
            transition: all 0.2s ease-in-out;
            box-shadow: var(--shadow-sm);
        }

        .search-input-wrapper:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .search-icon {
            color: var(--text-muted);
            margin-right: 0.75rem;
            font-size: 0.9rem;
        }

        .search-input {
            border: none;
            outline: none;
            background: transparent;
            flex: 1;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        .search-input::placeholder {
            color: var(--text-muted);
        }

        .search-shortcut {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            margin-left: 0.75rem;
            color: var(--text-muted);
            font-size: 0.75rem;
        }

        .search-shortcut kbd {
            background: var(--light-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.25rem;
            padding: 0.125rem 0.375rem;
            font-size: 0.7rem;
            font-family: inherit;
            color: var(--text-secondary);
        }

        /* Right Section */
        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .quick-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quick-action-btn {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            border: none;
            background: var(--card-bg);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            position: relative;
            box-shadow: var(--shadow-sm);
        }

        .quick-action-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            text-decoration: none;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.6rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            border: 2px solid white;
        }

        /* User Profile Section */
        .user-profile-dropdown {
            position: relative;
        }

        .user-profile-toggle {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 1rem;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            box-shadow: var(--shadow-sm);
            min-width: 200px;
        }

        .user-profile-toggle:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .user-avatar-container {
            position: relative;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--border-color);
        }

        .user-avatar-placeholder {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            border: 2px solid var(--border-color);
        }

        .user-status-indicator {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            background: var(--success-color);
            border: 2px solid white;
            border-radius: 50%;
        }

        .user-info {
            flex: 1;
            text-align: left;
            line-height: 1.3;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
            margin-bottom: 0.125rem;
        }

        .user-role {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .dropdown-arrow {
            color: var(--text-muted);
            font-size: 0.75rem;
            transition: transform 0.2s ease-in-out;
        }

        .user-profile-toggle[aria-expanded="true"] .dropdown-arrow {
            transform: rotate(180deg);
        }

        /* Modern Dropdown Menu */
        .modern-dropdown-menu {
            min-width: 360px;
            border: none;
            border-radius: var(--radius-lg);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            padding: 0;
            overflow: hidden;
            margin-top: 0.5rem;
            background: var(--card-bg);
        }

        .dropdown-user-header {
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
        }

        .user-header-avatar {
            position: relative;
        }

        .header-avatar-img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .header-avatar-placeholder {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .header-avatar-initials {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }

        .user-status-dot {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 16px;
            height: 16px;
            background: var(--success-color);
            border: 3px solid white;
            border-radius: 50%;
        }

        .user-header-info {
            flex: 1;
        }

        .user-header-name {
            font-size: 1.125rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            color: white;
        }

        .user-header-email {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 0.5rem;
        }

        .role-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .dropdown-quick-stats {
            padding: 1.5rem;
            background: var(--light-bg);
            border-bottom: 1px solid var(--border-color);
        }

        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stats-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .stats-refresh-btn {
            width: 28px;
            height: 28px;
            border: none;
            background: var(--card-bg);
            color: var(--text-secondary);
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            font-size: 0.75rem;
        }

        .stats-refresh-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: rotate(180deg);
        }

        .stats-refresh-btn.loading {
            animation: spin 1s linear infinite;
        }

        .stats-grid {
            display: flex;
            gap: 1rem;
        }

        .quick-stat-item {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }

        .stat-info {
            flex: 1;
        }

        .stat-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
            transition: all 0.3s ease-in-out;
        }

        .stat-growth {
            font-size: 0.7rem;
            font-weight: 600;
            padding: 0.125rem 0.375rem;
            border-radius: var(--radius-sm);
            line-height: 1;
        }

        .stat-growth.positive {
            background: #dcfce7;
            color: #166534;
        }

        .stat-growth.negative {
            background: #fef2f2;
            color: #dc2626;
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 0.125rem;
        }

        .stat-sublabel {
            font-size: 0.7rem;
            color: var(--text-muted);
            font-weight: 400;
        }

        .projects-icon {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }

        .messages-icon {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .quick-stat-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .quick-stat-item:hover .stat-number {
            color: var(--primary-color);
        }

        /* Loading and Animation States */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-8px,0); }
            70% { transform: translate3d(0,-4px,0); }
            90% { transform: translate3d(0,-2px,0); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate__animated {
            animation-duration: 1s;
            animation-fill-mode: both;
        }

        .animate__pulse {
            animation-name: pulse;
        }

        .animate__bounce {
            animation-name: bounce;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        /* Stat number loading state */
        .stat-number .fa-spinner {
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        .stat-number .fa-exclamation-triangle {
            color: var(--warning-color);
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Enhanced hover effects for quick stats */
        .quick-stat-item {
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .quick-stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .quick-stat-item:hover::before {
            left: 100%;
        }

        .quick-stat-item:active {
            transform: translateY(-1px) scale(0.98);
        }

        .dropdown-menu-items {
            padding: 1rem 0;
        }

        .modern-dropdown-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.2s ease-in-out;
            border: none;
            background: none;
            width: 100%;
        }

        .modern-dropdown-item:hover {
            background: var(--light-bg);
            color: var(--text-primary);
            text-decoration: none;
            transform: translateX(4px);
        }

        .modern-dropdown-item.logout-item {
            color: var(--danger-color);
        }

        .modern-dropdown-item.logout-item:hover {
            background: #fef2f2;
            color: var(--danger-color);
        }

        .item-icon {
            width: 40px;
            height: 40px;
            background: var(--light-bg);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            transition: all 0.2s ease-in-out;
        }

        .modern-dropdown-item:hover .item-icon {
            background: var(--primary-color);
            color: white;
        }

        .modern-dropdown-item.logout-item .item-icon {
            background: #fef2f2;
            color: var(--danger-color);
        }

        .modern-dropdown-item.logout-item:hover .item-icon {
            background: var(--danger-color);
            color: white;
        }

        .item-content {
            flex: 1;
        }

        .item-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.125rem;
        }

        .item-subtitle {
            font-size: 0.75rem;
            color: var(--text-muted);
            line-height: 1.3;
        }

        .item-arrow {
            color: var(--text-muted);
            font-size: 0.75rem;
            transition: all 0.2s ease-in-out;
        }

        .modern-dropdown-item:hover .item-arrow {
            color: var(--primary-color);
            transform: translateX(2px);
        }

        .dropdown-footer {
            padding: 1rem 1.5rem;
            background: var(--light-bg);
            border-top: 1px solid var(--border-color);
        }

        .last-login-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        /* Responsive Design */
        @media (max-width: 1199.98px) {
            .navbar-center {
                display: none !important;
            }

            .navbar-container {
                padding: 0 1rem;
            }

            .user-profile-toggle {
                min-width: auto;
                padding: 0.5rem;
            }

            .modern-dropdown-menu {
                min-width: 320px;
            }
        }

        @media (max-width: 991.98px) {
            .mobile-menu-toggle {
                display: flex;
            }

            .quick-actions {
                gap: 0.25rem;
            }

            .quick-action-btn {
                width: 36px;
                height: 36px;
            }
        }

        @media (max-width: 767.98px) {
            .navbar-container {
                padding: 0 0.75rem;
            }

            .navbar-left {
                gap: 1rem;
            }

            .quick-actions {
                display: none;
            }

            .modern-dropdown-menu {
                min-width: 280px;
            }

            .dropdown-quick-stats {
                flex-direction: column;
                gap: 0.75rem;
            }
        }

        .dropdown-menu {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            padding: 0.5rem 0;
            min-width: 200px;
            z-index: 1050;
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
            display: flex;
            align-items: center;
        }

        .dropdown-item:hover {
            background-color: var(--light-bg);
            color: var(--text-primary);
        }

        .dropdown-item.text-danger:hover {
            background-color: #fef2f2;
            color: var(--danger-color);
        }

        .dropdown-toggle::after {
            margin-left: 0.5rem;
            vertical-align: middle;
        }

        .dropdown-toggle:focus {
            box-shadow: none;
        }

        /* Enhanced User Avatar Styling */
        .user-avatar {
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.2s ease-in-out;
        }

        .user-avatar:hover {
            border-color: rgba(255, 255, 255, 0.4);
            transform: scale(1.05);
        }

        .user-avatar-placeholder {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.2s ease-in-out;
        }

        .user-avatar-placeholder:hover {
            border-color: rgba(255, 255, 255, 0.4);
            transform: scale(1.05);
        }

        .user-avatar-placeholder-large {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
            border: 2px solid var(--primary-color);
        }

        .user-avatar-large {
            border: 2px solid var(--primary-color);
        }

        .avatar-initials {
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .avatar-initials-large {
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        /* User Info Styling */
        .user-info {
            line-height: 1.2;
        }

        .user-name {
            font-weight: 500;
            font-size: 0.875rem;
            color: white;
            margin-bottom: 0;
        }

        .user-role {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }

        /* User Dropdown Toggle */
        .user-dropdown-toggle {
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            transition: all 0.2s ease-in-out;
        }

        .user-dropdown-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Enhanced Dropdown Menu */
        .user-dropdown-menu {
            min-width: 320px;
            border: none;
            border-radius: var(--radius-lg);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            padding: 0;
            overflow: hidden;
        }

        .user-dropdown-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            border-bottom: none;
            margin-bottom: 0;
        }

        .user-dropdown-info {
            flex: 1;
        }

        .user-dropdown-name {
            font-size: 1rem;
            color: white;
            margin-bottom: 0.25rem;
        }

        .user-dropdown-email {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 0.5rem;
        }

        .user-dropdown-role .badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            background-color: rgba(255, 255, 255, 0.2) !important;
            color: white;
        }

        .user-last-login {
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding-top: 0.75rem;
        }

        .user-last-login small {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        /* Enhanced Dropdown Items */
        .dropdown-item {
            padding: 1rem 1.5rem;
            border: none;
            transition: all 0.2s ease-in-out;
            display: flex;
            align-items: center;
        }

        .dropdown-item:hover {
            background-color: var(--light-bg);
            transform: translateX(2px);
        }

        .dropdown-item i {
            width: 20px;
            text-align: center;
            margin-right: 0.75rem;
        }

        .dropdown-item-content {
            flex: 1;
        }

        .dropdown-item-title {
            font-weight: 500;
            font-size: 0.875rem;
            margin-bottom: 0.125rem;
        }

        .dropdown-item-desc {
            font-size: 0.75rem;
            line-height: 1.2;
        }

        .dropdown-item.text-danger:hover {
            background-color: #fef2f2;
            color: var(--danger-color);
        }

        .dropdown-item.text-danger .dropdown-item-desc {
            color: rgba(239, 68, 68, 0.7) !important;
        }

        /* Notification Badge */
        .nav-link .badge {
            font-size: 0.6rem;
            padding: 0.2rem 0.4rem;
            min-width: 1.2rem;
            height: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Page Header */
        .page-header {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .page-header h1 {
            margin-bottom: 0;
            font-size: 1.75rem;
            font-weight: 700;
        }

        /* Performance Optimizations */

        /* Image optimization and lazy loading */
        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        img[data-src] {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        img[data-src].loaded {
            opacity: 1;
        }

        /* Optimized image containers */
        .image-container {
            position: relative;
            overflow: hidden;
            border-radius: var(--radius-md);
        }

        .image-placeholder {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: var(--radius-md);
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Optimized table rendering */
        .table-responsive {
            will-change: scroll-position;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--light-bg);
        }

        .table-responsive::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: var(--light-bg);
            border-radius: var(--radius-sm);
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: var(--radius-sm);
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: var(--primary-hover);
        }

        /* Efficient animations */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Reduce motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Enhanced Responsive Design */

        /* Extra small devices (portrait phones, less than 576px) */
        @media (max-width: 575.98px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding-top: 0;
            }

            main {
                margin-left: 0;
                padding: 0.75rem;
            }

            .navbar-brand {
                padding: 0.75rem;
                font-size: 0.9rem;
            }

            .stats-card {
                margin-bottom: 0.75rem;
                padding: 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .page-header {
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .page-header h1 {
                font-size: 1.5rem;
            }

            .table th,
            .table td {
                padding: 0.5rem;
                font-size: 0.8rem;
            }
        }

        /* Small devices (landscape phones, 576px and up) */
        @media (min-width: 576px) and (max-width: 767.98px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding-top: 0;
            }

            main {
                margin-left: 0;
                padding: 1rem;
            }

            .stats-card {
                margin-bottom: 1rem;
            }
        }

        /* Medium devices (tablets, 768px and up) */
        @media (min-width: 768px) and (max-width: 991.98px) {
            .sidebar {
                width: var(--sidebar-width);
                position: fixed;
                left: 0;
                top: 0;
                bottom: 0;
                padding-top: 60px;
            }

            main {
                margin-left: var(--sidebar-width);
                padding: 1.5rem;
            }
        }

        /* Large devices (desktops, 992px and up) */
        @media (min-width: 992px) {
            main {
                padding: 2rem;
            }

            .stats-card {
                padding: 2rem;
            }

            .card-body {
                padding: 2rem;
            }
        }

        /* Extra large devices (large desktops, 1200px and up) */
        @media (min-width: 1200px) {
            main {
                padding: 2.5rem;
            }

            .page-header {
                padding: 2rem 2.5rem;
            }
        }

        /* Responsive Utility Classes */

        /* Display utilities */
        .d-xs-none { display: none !important; }
        .d-xs-inline { display: inline !important; }
        .d-xs-inline-block { display: inline-block !important; }
        .d-xs-block { display: block !important; }
        .d-xs-flex { display: flex !important; }
        .d-xs-grid { display: grid !important; }

        @media (min-width: 576px) {
            .d-sm-none { display: none !important; }
            .d-sm-inline { display: inline !important; }
            .d-sm-inline-block { display: inline-block !important; }
            .d-sm-block { display: block !important; }
            .d-sm-flex { display: flex !important; }
            .d-sm-grid { display: grid !important; }
        }

        @media (min-width: 768px) {
            .d-md-none { display: none !important; }
            .d-md-inline { display: inline !important; }
            .d-md-inline-block { display: inline-block !important; }
            .d-md-block { display: block !important; }
            .d-md-flex { display: flex !important; }
            .d-md-grid { display: grid !important; }
        }

        @media (min-width: 992px) {
            .d-lg-none { display: none !important; }
            .d-lg-inline { display: inline !important; }
            .d-lg-inline-block { display: inline-block !important; }
            .d-lg-block { display: block !important; }
            .d-lg-flex { display: flex !important; }
            .d-lg-grid { display: grid !important; }
        }

        @media (min-width: 1200px) {
            .d-xl-none { display: none !important; }
            .d-xl-inline { display: inline !important; }
            .d-xl-inline-block { display: inline-block !important; }
            .d-xl-block { display: block !important; }
            .d-xl-flex { display: flex !important; }
            .d-xl-grid { display: grid !important; }
        }

        /* Text utilities */
        .text-xs-left { text-align: left !important; }
        .text-xs-center { text-align: center !important; }
        .text-xs-right { text-align: right !important; }

        @media (min-width: 576px) {
            .text-sm-left { text-align: left !important; }
            .text-sm-center { text-align: center !important; }
            .text-sm-right { text-align: right !important; }
        }

        @media (min-width: 768px) {
            .text-md-left { text-align: left !important; }
            .text-md-center { text-align: center !important; }
            .text-md-right { text-align: right !important; }
        }

        /* Spacing utilities */
        .p-responsive { padding: var(--spacing-md); }
        .m-responsive { margin: var(--spacing-md); }
        .gap-responsive { gap: var(--spacing-md); }

        /* Container utilities */
        .container-fluid-responsive {
            width: 100%;
            padding-right: var(--spacing-md);
            padding-left: var(--spacing-md);
            margin-right: auto;
            margin-left: auto;
        }

        @media (min-width: 576px) {
            .container-fluid-responsive {
                max-width: 540px;
                padding-right: var(--spacing-lg);
                padding-left: var(--spacing-lg);
            }
        }

        @media (min-width: 768px) {
            .container-fluid-responsive {
                max-width: 720px;
            }
        }

        @media (min-width: 992px) {
            .container-fluid-responsive {
                max-width: 960px;
            }
        }

        @media (min-width: 1200px) {
            .container-fluid-responsive {
                max-width: 1140px;
            }
        }

        /* Performance optimizations */
        .will-change-transform { will-change: transform; }
        .will-change-scroll { will-change: scroll-position; }
        .will-change-opacity { will-change: opacity; }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

    </style>
</head>
<body>
    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Modern Sidebar -->
    <nav id="sidebarMenu" class="sidebar collapse d-lg-block">
        <div class="sidebar-sticky">
            <!-- Sidebar Brand -->
            <div class="sidebar-brand">
                <div class="brand-icon">
                    <i class="fas fa-hard-hat"></i>
                </div>
                <div class="brand-text">
                    <h5>Flori Construction</h5>
                    <small>Admin Panel</small>
                </div>
            </div>

            <!-- Navigation Menu -->
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'projects.php' ? 'active' : ''; ?>" href="projects.php">
                        <i class="fas fa-building"></i>
                        Projects
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'services.php' ? 'active' : ''; ?>" href="services.php">
                        <i class="fas fa-tools"></i>
                        Services
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'media.php' ? 'active' : ''; ?>" href="media.php">
                        <i class="fas fa-images"></i>
                        Media
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'messages.php' ? 'active' : ''; ?>" href="messages.php">
                        <i class="fas fa-envelope"></i>
                        Messages
                        <?php
                        try {
                            $database = new Database();
                            $conn = $database->getConnection();
                            $newMessagesCount = $conn->query("SELECT COUNT(*) FROM messages WHERE status = 'new'")->fetchColumn();
                            if ($newMessagesCount > 0) {
                                echo '<span class="badge bg-danger ms-auto">' . $newMessagesCount . '</span>';
                            }
                        } catch (Exception $e) {
                            // Silently handle error
                        }
                        ?>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>" href="users.php">
                        <i class="fas fa-users"></i>
                        Users
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>" href="settings.php">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading">
                <span>Quick Actions</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="project-add.php">
                        <i class="fas fa-plus"></i>
                        Add Project
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="service-add.php">
                        <i class="fas fa-plus"></i>
                        Add Service
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="media-upload.php">
                        <i class="fas fa-upload"></i>
                        Upload Media
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading">
                <span>Website</span>
            </h6>
            <ul class="nav flex-column mb-2">
                <li class="nav-item">
                    <a class="nav-link" href="../index.php" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        View Website
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="logout.php">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Top Navbar -->
    <nav class="top-navbar">
        <div class="navbar-content">
            <div class="navbar-left">
                <h1 class="page-title"><?php echo isset($pageTitle) ? $pageTitle : 'Dashboard'; ?></h1>
            </div>

            <div class="navbar-right">
                <div class="search-container d-none d-md-block">
                    <i class="search-icon fas fa-search"></i>
                    <input type="text" class="search-input" placeholder="Search..." id="globalSearch">
                </div>

                <div class="quick-actions">
                    <a href="messages.php" class="quick-action-btn" title="Messages">
                        <i class="fas fa-envelope"></i>
                        <?php
                        try {
                            $database = new Database();
                            $conn = $database->getConnection();
                            $newMessagesCount = $conn->query("SELECT COUNT(*) FROM messages WHERE status = 'new'")->fetchColumn();
                            if ($newMessagesCount > 0) {
                                echo '<span class="notification-badge">' . $newMessagesCount . '</span>';
                            }
                        } catch (Exception $e) {
                            // Silently handle error
                        }
                        ?>
                    </a>

                    <div class="dropdown">
                        <button class="quick-action-btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="User Menu">
                            <i class="fas fa-user"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                            <li class="user-info">
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-3">
                                        <i class="fas fa-user-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <div class="user-name"><?php echo htmlspecialchars($_SESSION['username']); ?></div>
                                        <div class="user-email">Administrator</div>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>


